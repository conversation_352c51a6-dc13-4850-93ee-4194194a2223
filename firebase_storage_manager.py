# -*- coding: utf-8 -*-
"""
مدير Firebase Storage لتخزين ملفات المودات
Firebase Storage Manager for Mod Files
"""

import os
import json
import time
import hashlib
import requests
from typing import Optional, Dict, Any, List
from urllib.parse import urlparse
import tempfile

try:
    import firebase_admin
    from firebase_admin import credentials, storage
    FIREBASE_AVAILABLE = True
except ImportError:
    FIREBASE_AVAILABLE = False
    print("⚠️ firebase-admin غير متوفر. سيتم تعطيل رفع الملفات.")




# دالة مساعدة للاستخدام السهل
from firebase_config import firebase_manager as global_firebase_manager

def create_firebase_manager():
    """إنشاء مدير Firebase Storage مع حلول بديلة"""

    # أولاً: محاولة استخدام FirebaseManager المهيأ عالمياً من firebase_config.py
    if global_firebase_manager and global_firebase_manager.is_initialized:
        print("✅ تم استخدام FirebaseManager المهيأ عالمياً.")
        return global_firebase_manager
    
    # ثانياً: محاولة نظام التخزين المحلي (الأولوية الأخيرة الآن)
    try:
        print("🔄 محاولة استخدام نظام التخزين المحلي...")
        from local_file_storage import LocalFileStorage
        local_storage = LocalFileStorage()
        if local_storage.is_initialized:
            print("✅ تم تهيئة نظام التخزين المحلي بنجاح")
            return local_storage
    except Exception as local_error:
        print(f"⚠️ فشل نظام التخزين المحلي: {local_error}")

    # إذا فشل كل شيء
    print("❌ فشل في جميع أنظمة التخزين")
    print("💡 نصائح:")
    print("   - تأكد من صلاحيات الكتابة في مجلد المشروع")
    print("   - راجع إعدادات Firebase في FIREBASE_TROUBLESHOOTING.md")
    print("   - تحقق من اتصالك بالإنترنت")

    return None


# اختبار سريع
if __name__ == "__main__":
    print("🧪 اختبار Firebase Storage Manager...")
    
    manager = create_firebase_manager()
    
    if manager and manager.is_initialized:
        print("✅ تم تهيئة Firebase بنجاح")
        
        # فحص الاتصال
        status = manager.check_connection()
        print(f"📊 حالة الاتصال: {status}")
        
        # إحصائيات التخزين
        stats = manager.get_storage_stats()
        print(f"📈 إحصائيات التخزين: {stats}")
        
    else:
        print("❌ فشل في تهيئة Firebase")
